﻿{
    "TestSuite":  "AutoGen Studio Chat API Tests",
    "ExecutionTime":  "2025-08-04 20:58:21",
    "Summary":  {
                    "SuccessRate":  "100%",
                    "TotalTests":  12,
                    "PassedTests":  12,
                    "FailedTests":  0
                },
    "Configuration":  {
                          "BaseURL":  "http://localhost:1113",
                          "OpenAIBaseURL":  "https://gnomic.nengyongai.cn/v1",
                          "APIKey":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
                      },
    "TestResults":  [
                        {
                            "TestDescription":  "获取API基本信息",
                            "Timestamp":  "2025-08-04 20:57:53",
                            "Request":  {
                                            "Endpoint":  "/",
                                            "Method":  "GET",
                                            "Body":  null
                                        },
                            "Response":  {
                                             "Content":  "{\"message\":\"AutoGen Studio Chat API\",\"version\":\"1.0.0\",\"endpoints\":{\"chat\":\"/chat\",\"chat_stream\":\"/chat/stream\",\"health\":\"/health\"}}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test1_RootEndpoint"
                        },
                        {
                            "TestDescription":  "健康检查",
                            "Timestamp":  "2025-08-04 20:57:53",
                            "Request":  {
                                            "Endpoint":  "/health",
                                            "Method":  "GET",
                                            "Body":  null
                                        },
                            "Response":  {
                                             "Content":  "{\"status\":\"healthy\",\"service\":\"autogen-studio-chat-api\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test2_HealthCheck"
                        },
                        {
                            "TestDescription":  "非流式聊天 - 基本对话",
                            "Timestamp":  "2025-08-04 20:57:55",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "你好，请介绍一下你自己",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"ad8ca3e8-64c9-4739-8149-149fa74315ee\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=48, completion_tokens=49)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:57:55.293093+00:00\\\",\\n    \\\"content\\\": \\\"你好！我是您的AI助手，可以帮助您解答问题、提供建议和执行任务。有什么我可以帮助您的吗？\\\\n\\\\nTerMINATE\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test3_NonStreamChat_Basic"
                        },
                        {
                            "TestDescription":  "非流式聊天 - 机器学习问题",
                            "Timestamp":  "2025-08-04 20:57:58",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "解释一下机器学习的基本概念",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"2197bd11-3fe7-4fb4-89e5-d10ab65ea69f\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=52, completion_tokens=213)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:57:58.304203+00:00\\\",\\n    \\\"content\\\": \\\"机器学习是一种人工智能的技术，其目标是让计算机系统通过数据学习和改进，而无需进行明确地编程。基本上，机器学习的目标是通过建立模型来识别数据中的模式和规律。这些模型可以根据输入数据进行训练，以预测未来的数据。机器学习可以分为监督学习、无监督学习和强化学习等不同类型。监督学习是根据带有标签的训练数据进行训练，无监督学习是使用未标记的数据进行训练，强化学习是通过不断尝试和错误来优化决策。\\\\n\\\\nTerminateterminate\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test4_NonStreamChat_ML"
                        },
                        {
                            "TestDescription":  "非流式聊天 - 带API密钥",
                            "Timestamp":  "2025-08-04 20:58:01",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "写一个Python函数来计算斐波那契数列",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"3f2d63b9-d2b7-417a-a08f-0935e4fe375b\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=53, completion_tokens=165)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:01.320115+00:00\\\",\\n    \\\"content\\\": \\\"以下是一个Python函数，用于计算斐波那契数列的第n项：\\\\n\\\\n```python\\\\ndef fibonacci(n):\\\\n    if n \u003c= 1:\\\\n        return n\\\\n    else:\\\\n        return fibonacci(n-1) + fibonacci(n-2)\\\\n\\\\n# 测试\\\\nn = 10\\\\nresult = fibonacci(n)\\\\nprint(f\\\\\\\"The {n}th Fibonacci number is: {result}\\\\\\\")\\\\n```\\\\n\\\\n这个函数将计算斐波那契数列的第n项。请记住，这个方法在计算大数时可能效率不高。你可以将n设置为其他值来测试。请注意，当n很大时，计算将变得很慢。\\\\n\\\\nTERMINATE\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test5_NonStreamChat_WithAPIKey"
                        },
                        {
                            "TestDescription":  "非流式聊天 - Ollama模型",
                            "Timestamp":  "2025-08-04 20:58:04",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "model_name":  "qwen3:latest",
                                                         "messages":  [
                                                                          {
                                                                              "content":  "你好世界",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "stream":  false,
                                                         "model_type":  "ollama"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"380c905a-6e92-443e-8ee9-7aa2325c5c4a\\\",\\n    \\\"source\\\": \\\"ollama_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=39, completion_tokens=89)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:04.514821+00:00\\\",\\n    \\\"content\\\": \\\"\u003cthink\u003e\\\\n好的，用户打招呼说“你好世界”，我应该回应一个友好的问候。作为AI助手，我需要保持礼貌和帮助的态度。可以简单回复“你好！有什么我可以帮助你的吗？”这样既回应了问候，又主动提供帮助，符合用户的需求。不需要使用复杂工具，直接回答即可。确认没有其他任务需要处理，可以终止流程。\\\\n\u003c/think\u003e\\\\n\\\\n你好！有什么我可以帮助你的吗？\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test6_NonStreamChat_Ollama"
                        },
                        {
                            "TestDescription":  "流式聊天 - 基本对话",
                            "Timestamp":  "2025-08-04 20:58:07",
                            "Request":  {
                                            "Endpoint":  "/chat/stream",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "写一首关于春天的诗",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  true,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "data: {\"id\": \"046445e4-5d63-41be-bb3a-5f467e114eac\", \"source\": \"openai_agent\", \"models_usage\": \"RequestUsage(prompt_tokens=47, completion_tokens=113)\", \"metadata\": {}, \"created_at\": \"2025-08-04 12:58:07.569481+00:00\", \"content\": \"春风拂面笑，百花吐艳开，\\n翠绿嫩叶新，春光满园来。\\n黄莺啼不断，蝶舞花间游，\\n春天美如画，心情愉悦畅。\\n春风送暖意，万物皆生机，\\n春天真美好，心中充满喜。\\n\\nTERMINATE\", \"type\": \"TextMessage\"}\n\ndata: {\"messages\": [\"id=\u002708f9d162-3790-45fd-90fe-d93d344aa15e\u0027 source=\u0027user\u0027 models_usage=None metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 58, 4, 526789, tzinfo=datetime.timezone.utc) content=\u0027写一首关于春天的诗\u0027 type=\u0027TextMessage\u0027\", \"id=\u0027046445e4-5d63-41be-bb3a-5f467e114eac\u0027 source=\u0027openai_agent\u0027 models_usage=RequestUsage(prompt_tokens=47, completion_tokens=113) metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 58, 7, 569481, tzinfo=datetime.timezone.utc) content=\u0027春风拂面笑，百花吐艳开，\\\\n翠绿嫩叶新，春光满园来。\\\\n黄莺啼不断，蝶舞花间游，\\\\n春天美如画，心情愉悦畅。\\\\n春风送暖意，万物皆生机，\\\\n春天真美好，心中充满喜。\\\\n\\\\nTERMINATE\u0027 type=\u0027TextMessage\u0027\"], \"stop_reason\": null}\n\ndata: {\"type\": \"end\"}\n\n",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test7_StreamChat_Basic"
                        },
                        {
                            "TestDescription":  "流式聊天 - 讲笑话",
                            "Timestamp":  "2025-08-04 20:58:08",
                            "Request":  {
                                            "Endpoint":  "/chat/stream",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "讲个笑话",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  true,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "data: {\"id\": \"f0215cf6-5248-4e46-9f4a-0a41e02f0cbe\", \"source\": \"openai_agent\", \"models_usage\": \"RequestUsage(prompt_tokens=41, completion_tokens=24)\", \"metadata\": {}, \"created_at\": \"2025-08-04 12:58:08.962609+00:00\", \"content\": \"为什么冰箱会笑？因为有冷笑话在里面！\\n\\nTERMINATE\", \"type\": \"TextMessage\"}\n\ndata: {\"messages\": [\"id=\u0027c39cd05a-7821-4d33-a92e-1ec4ba697ab5\u0027 source=\u0027user\u0027 models_usage=None metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 58, 7, 584840, tzinfo=datetime.timezone.utc) content=\u0027讲个笑话\u0027 type=\u0027TextMessage\u0027\", \"id=\u0027f0215cf6-5248-4e46-9f4a-0a41e02f0cbe\u0027 source=\u0027openai_agent\u0027 models_usage=RequestUsage(prompt_tokens=41, completion_tokens=24) metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 58, 8, 962609, tzinfo=datetime.timezone.utc) content=\u0027为什么冰箱会笑？因为有冷笑话在里面！\\\\n\\\\nTERMINATE\u0027 type=\u0027TextMessage\u0027\"], \"stop_reason\": null}\n\ndata: {\"type\": \"end\"}\n\n",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test8_StreamChat_Joke"
                        },
                        {
                            "TestDescription":  "多轮对话测试",
                            "Timestamp":  "2025-08-04 20:58:11",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "我想学习编程",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          },
                                                                          {
                                                                              "content":  "很好！编程是一项很有用的技能。你想学习哪种编程语言呢？",
                                                                              "type":  "text",
                                                                              "role":  "assistant"
                                                                          },
                                                                          {
                                                                              "content":  "我想学Python",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"35e6b17e-b974-4911-9058-de2f8f5408be\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=93, completion_tokens=53)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:11.022739+00:00\\\",\\n    \\\"content\\\": \\\"学习Python是个很好的选择！你可以开始通过在线教程、书籍或者学习平台来学习Python编程。有什么我可以帮忙的吗？\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test9_MultiTurnChat"
                        },
                        {
                            "TestDescription":  "带MCP服务器URL的请求",
                            "Timestamp":  "2025-08-04 20:58:13",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "测试MCP功能",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "mcp_url_list":  [
                                                                              "http://localhost:1111/mcp"
                                                                          ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-3.5-turbo"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"330c0886-3380-40a7-a9e9-645bf2f54e28\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=242, completion_tokens=13)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:13.151275+00:00\\\",\\n    \\\"content\\\": [\\n      \\\"FunctionCall(id=\u0027call_viIu7pvo04DqBJvngoFrNChu\u0027, arguments=\u0027{\\\\\\\"x\\\\\\\":100}\u0027, name=\u0027log\u0027)\\\"\\n    ],\\n    \\\"type\\\": \\\"ToolCallRequestEvent\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"9ca7b312-ad95-4b65-98f4-8a4a29ae7a4e\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": null,\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:13.181913+00:00\\\",\\n    \\\"content\\\": [\\n      \\\"content=\u00272.0\u0027 name=\u0027log\u0027 call_id=\u0027call_viIu7pvo04DqBJvngoFrNChu\u0027 is_error=False\\\"\\n    ],\\n    \\\"type\\\": \\\"ToolCallExecutionEvent\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"9b9a8e0e-9139-4626-9c13-48cc5ca0fbfe\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=63, completion_tokens=23)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:13.947087+00:00\\\",\\n    \\\"content\\\": \\\"The result of testing the MCP function with x=100 is 2.0. Task completed. TERMINATE.\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test10_MCPRequest"
                        },
                        {
                            "TestDescription":  "带团队ID的请求",
                            "Timestamp":  "2025-08-04 20:58:18",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "中国的首都是哪里",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "team_id":  6
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"f398d265-c8b4-4d64-a94e-4bd2a84e7074\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{\u0027prompt_tokens\u0027: 184, \u0027completion_tokens\u0027: 113}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04T12:58:17.927354Z\\\",\\n    \\\"content\\\": \\\"\u003cthink\u003e\\\\n好的，用户问的是“中国的首都是哪里”。首先，我需要确认这个问题是否需要使用提供的工具。用户的问题是关于地理知识的，而提供的工具是一个计算器，用于基本的算术运算。显然，这个问题不需要进行计算，而是需要常识性的答案。因此，我应该直接回答，而不需要调用任何函数。接下来，我需要确认中国的首都是北京，然后用中文简洁地回复用户。确保回答准确且友好，不需要额外的信息或工具调用。\\\\n\u003c/think\u003e\\\\n\\\\n中国的首都是北京。\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"88ebb53d-c9cf-438d-9993-39bc4d110234\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{\u0027prompt_tokens\u0027: 296, \u0027completion_tokens\u0027: 1}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04T12:58:18.001627Z\\\",\\n    \\\"content\\\": \\\"\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"570f680e-9897-4079-8b8d-081695de98e0\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{\u0027prompt_tokens\u0027: 296, \u0027completion_tokens\u0027: 4}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04T12:58:18.156314Z\\\",\\n    \\\"content\\\": \\\"_TERMINATE_\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"dcc47e9d-ae29-467c-8ccd-ad1265b8be3e\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{\u0027prompt_tokens\u0027: 299, \u0027completion_tokens\u0027: 1}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04T12:58:18.255091Z\\\",\\n    \\\"content\\\": \\\"\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test11_TeamIDRequest"
                        },
                        {
                            "TestDescription":  "图片消息测试",
                            "Timestamp":  "2025-08-04 20:58:21",
                            "Request":  {
                                            "Endpoint":  "/chat",
                                            "Method":  "POST",
                                            "Body":  {
                                                         "messages":  [
                                                                          {
                                                                              "content":  "1_1752497070478.png",
                                                                              "type":  "image",
                                                                              "role":  "user"
                                                                          },
                                                                          {
                                                                              "content":  "图片中讲述了什么内容",
                                                                              "type":  "text",
                                                                              "role":  "user"
                                                                          }
                                                                      ],
                                                         "model_type":  "openai",
                                                         "openai_base_url":  "https://gnomic.nengyongai.cn/v1",
                                                         "stream":  false,
                                                         "api_key":  "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384",
                                                         "model_name":  "gpt-4o-mini"
                                                     }
                                        },
                            "Response":  {
                                             "Content":  "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"01d1f1eb-8375-4b76-b283-d09d82cfc3b5\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=8572, completion_tokens=99)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:58:21.979997+00:00\\\",\\n    \\\"content\\\": \\\"图片中显示了一段代码，涉及图像处理，可能是在使用OpenCV库进行图像的边界反射和缩放操作。具体功能包括：\\\\n\\\\n1. `borderType` 设置为 `cv2.BORDER_REFLECT101`，这是一种处理边界的方式。\\\\n2. 使用 `cv2.resize` 函数将图像调整为640x640的大小。\\\\n\\\\n如果你需要更详细的解释或有其他问题，请告诉我！\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}",
                                             "ErrorMessage":  "",
                                             "StatusCode":  200,
                                             "Success":  true
                                         },
                            "TestName":  "Test15_ImageMessage"
                        }
                    ]
}